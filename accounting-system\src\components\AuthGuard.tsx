'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';

interface AuthGuardProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'accountant' | 'employee';
}

export default function AuthGuard({ children, requiredRole }: AuthGuardProps) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const userData = localStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          
          // Check role permissions
          if (requiredRole) {
            const roleHierarchy = { admin: 3, accountant: 2, employee: 1 };
            const userLevel = roleHierarchy[parsedUser.role as keyof typeof roleHierarchy] || 0;
            const requiredLevel = roleHierarchy[requiredRole];
            
            if (userLevel < requiredLevel) {
              router.push('/unauthorized');
              return;
            }
          }
        } else {
          router.push('/login');
          return;
        }
      } catch (error) {
        console.error('Auth check error:', error);
        router.push('/login');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router, requiredRole]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
}
