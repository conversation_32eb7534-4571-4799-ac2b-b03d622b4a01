'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import DashboardLayout from '@/components/DashboardLayout';
import {
  BuildingOfficeIcon,
  UserIcon,
  CogIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BellIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

export default function SettingsPage() {
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState('company');

  const tabs = [
    { id: 'company', name: 'معلومات الشركة', icon: BuildingOfficeIcon },
    { id: 'users', name: 'إدارة المستخدمين', icon: UserIcon },
    { id: 'accounting', name: 'الإعدادات المحاسبية', icon: CurrencyDollarIcon },
    { id: 'system', name: 'إعدادات النظام', icon: CogIcon },
    { id: 'security', name: 'الأمان والخصوصية', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'الإشعارات', icon: BellIcon }
  ];

  const renderCompanySettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">معلومات الشركة الأساسية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
            <input type="text" className="input-field" defaultValue="شركة المحاسبة المتقدمة" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الرقم التجاري</label>
            <input type="text" className="input-field" defaultValue="1010123456" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الرقم الضريبي</label>
            <input type="text" className="input-field" defaultValue="*********789003" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
            <input type="text" className="input-field" defaultValue="+966501234567" />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
            <textarea className="input-field" rows={3} defaultValue="الرياض، المملكة العربية السعودية" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
            <input type="email" className="input-field" defaultValue="<EMAIL>" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الموقع الإلكتروني</label>
            <input type="url" className="input-field" defaultValue="https://company.com" />
          </div>
        </div>
        <div className="mt-6">
          <button className="btn-primary">حفظ التغييرات</button>
        </div>
      </div>
    </div>
  );

  const renderUserSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">إدارة المستخدمين</h3>
        <button className="btn-primary">إضافة مستخدم جديد</button>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">البريد الإلكتروني</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الدور</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر دخول</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-medium">أ</span>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">أحمد محمد</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  مدير
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                  نشط
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button className="text-primary-600 hover:text-primary-900 ml-4">تعديل</button>
                <button className="text-red-600 hover:text-red-900">حذف</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderAccountingSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">الإعدادات المحاسبية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">العملة الأساسية</label>
            <select className="input-field">
              <option value="SAR">ريال سعودي (SAR)</option>
              <option value="USD">دولار أمريكي (USD)</option>
              <option value="EUR">يورو (EUR)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">السنة المالية</label>
            <select className="input-field">
              <option value="calendar">سنة ميلادية (يناير - ديسمبر)</option>
              <option value="hijri">سنة هجرية</option>
              <option value="custom">سنة مالية مخصصة</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">معدل ضريبة القيمة المضافة</label>
            <div className="flex items-center">
              <input type="number" className="input-field w-24" defaultValue="15" />
              <span className="ml-2 text-gray-500">%</span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">طريقة تقييم المخزون</label>
            <select className="input-field">
              <option value="fifo">الوارد أولاً صادر أولاً (FIFO)</option>
              <option value="lifo">الوارد أخيراً صادر أولاً (LIFO)</option>
              <option value="average">المتوسط المرجح</option>
            </select>
          </div>
        </div>
        <div className="mt-6">
          <button className="btn-primary">حفظ الإعدادات</button>
        </div>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">إعدادات النظام العامة</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">اللغة الافتراضية</h4>
              <p className="text-sm text-gray-500">اللغة المستخدمة في واجهة النظام</p>
            </div>
            <select className="input-field w-48">
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">المنطقة الزمنية</h4>
              <p className="text-sm text-gray-500">المنطقة الزمنية المستخدمة في النظام</p>
            </div>
            <select className="input-field w-48">
              <option value="Asia/Riyadh">الرياض (GMT+3)</option>
              <option value="UTC">UTC</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">تنسيق التاريخ</h4>
              <p className="text-sm text-gray-500">تنسيق عرض التواريخ في النظام</p>
            </div>
            <select className="input-field w-48">
              <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
              <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
              <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
            </select>
          </div>
        </div>
        <div className="mt-6">
          <button className="btn-primary">حفظ الإعدادات</button>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return renderCompanySettings();
      case 'users':
        return renderUserSettings();
      case 'accounting':
        return renderAccountingSettings();
      case 'system':
        return renderSystemSettings();
      case 'security':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات الأمان والخصوصية</h3>
            <p className="text-gray-500">هذا القسم قيد التطوير</p>
          </div>
        );
      case 'notifications':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات الإشعارات</h3>
            <p className="text-gray-500">هذا القسم قيد التطوير</p>
          </div>
        );
      default:
        return renderCompanySettings();
    }
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">{t('navigation.settings')}</h1>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Settings Navigation */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="h-5 w-5 ml-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Settings Content */}
          <div className="flex-1">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
