'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import AuthGuard from './AuthGuard';
import Sidebar from './Sidebar';
import LanguageToggle from './LanguageToggle';
import Breadcrumb, { useBreadcrumb } from './Breadcrumb';
import { User } from '@/types';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'accountant' | 'employee';
}

export default function DashboardLayout({ children, requiredRole }: DashboardLayoutProps) {
  const [user, setUser] = useState<User | null>(null);
  const pathname = usePathname();
  const breadcrumbItems = useBreadcrumb(pathname);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  return (
    <AuthGuard requiredRole={requiredRole}>
      <div className="flex h-screen bg-gray-50">
        {user && <Sidebar userRole={user.role} />}
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
          {/* Top bar */}
          <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="flex items-center justify-between px-6 py-4">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  {/* Page title will be set by individual pages */}
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <LanguageToggle />
                {user && (
                  <div className="flex items-center">
                    <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-medium text-sm">
                        {user.name.charAt(0)}
                      </span>
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-700">
                      {user.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </header>

          {/* Main content */}
          <main className="flex-1 overflow-auto">
            <div className="p-6">
              {/* Breadcrumb */}
              {breadcrumbItems.length > 0 && (
                <div className="mb-6">
                  <Breadcrumb items={breadcrumbItems} />
                </div>
              )}
              {children}
            </div>
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
