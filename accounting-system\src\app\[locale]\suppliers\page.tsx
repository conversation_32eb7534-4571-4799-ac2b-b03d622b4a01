'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  TruckIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

export default function SuppliersPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data للموردين
  const suppliers = [
    {
      id: 1,
      name: 'شركة التوريدات المتقدمة',
      email: '<EMAIL>',
      phone: '+966501234567',
      address: 'الرياض، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: -6000, // سالب يعني مدين للمورد
      totalInvoices: 15,
      lastTransaction: '2024-01-12',
      status: 'active',
      category: 'مواد خام'
    },
    {
      id: 2,
      name: 'مؤسسة الخدمات التقنية',
      email: '<EMAIL>',
      phone: '+966507654321',
      address: 'جدة، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: 0,
      totalInvoices: 8,
      lastTransaction: '2024-01-08',
      status: 'active',
      category: 'خدمات تقنية'
    },
    {
      id: 3,
      name: 'شركة النقل السريع',
      email: '<EMAIL>',
      phone: '+966551234567',
      address: 'الدمام، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: -2500,
      totalInvoices: 12,
      lastTransaction: '2024-01-05',
      status: 'active',
      category: 'خدمات نقل'
    }
  ];

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600'; // دائن (المورد مدين لنا)
    if (balance < 0) return 'text-red-600';   // مدين (نحن مدينون للمورد)
    return 'text-gray-600';
  };

  const getBalanceText = (balance: number) => {
    if (balance > 0) return 'دائن';
    if (balance < 0) return 'مدين';
    return 'متوازن';
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">الموردين</h1>
          <button className="btn-primary flex items-center">
            <PlusIcon className="h-5 w-5 ml-2" />
            إضافة مورد جديد
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في الموردين..."
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select className="input-field">
              <option value="">جميع الفئات</option>
              <option value="raw-materials">مواد خام</option>
              <option value="tech-services">خدمات تقنية</option>
              <option value="transport">خدمات نقل</option>
              <option value="office-supplies">مستلزمات مكتبية</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <TruckIcon className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي الموردين</p>
                <p className="text-2xl font-bold text-gray-900">{suppliers.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي المستحق للموردين</p>
                <p className="text-2xl font-bold text-red-600">
                  {Math.abs(suppliers.filter(s => s.balance < 0).reduce((sum, s) => sum + s.balance, 0)).toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">المستحق من الموردين</p>
                <p className="text-2xl font-bold text-green-600">
                  {suppliers.filter(s => s.balance > 0).reduce((sum, s) => sum + s.balance, 0).toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 font-bold text-sm">فعال</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">الموردين النشطون</p>
                <p className="text-2xl font-bold text-purple-600">
                  {suppliers.filter(s => s.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Suppliers Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    اسم المورد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الفئة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البريد الإلكتروني
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الهاتف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرصيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عدد الفواتير
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر معاملة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {suppliers.map((supplier) => (
                  <tr key={supplier.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-orange-100 rounded-full flex items-center justify-center">
                          <TruckIcon className="h-5 w-5 text-orange-600" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{supplier.name}</div>
                          <div className="text-sm text-gray-500">{supplier.taxNumber}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {supplier.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {supplier.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {supplier.phone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">
                        <span className={`font-medium ${getBalanceColor(supplier.balance)}`}>
                          {Math.abs(supplier.balance).toLocaleString('ar-SA')} ر.س
                        </span>
                        <div className="text-xs text-gray-500">
                          {getBalanceText(supplier.balance)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {supplier.totalInvoices}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {supplier.lastTransaction}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button className="text-primary-600 hover:text-primary-900" title="كشف الحساب">
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900" title="تعديل">
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button className="text-red-600 hover:text-red-900" title="حذف">
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <PlusIcon className="h-5 w-5 text-gray-600 ml-2" />
              <span className="text-sm font-medium text-gray-700">إضافة مورد جديد</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <CurrencyDollarIcon className="h-5 w-5 text-gray-600 ml-2" />
              <span className="text-sm font-medium text-gray-700">تسجيل دفعة للمورد</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <TruckIcon className="h-5 w-5 text-gray-600 ml-2" />
              <span className="text-sm font-medium text-gray-700">إنشاء طلب شراء</span>
            </button>
          </div>
        </div>

        {/* Categories Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">الموردين حسب الفئة</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">1</div>
              <div className="text-sm text-gray-600">مواد خام</div>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-green-600">1</div>
              <div className="text-sm text-gray-600">خدمات تقنية</div>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">1</div>
              <div className="text-sm text-gray-600">خدمات نقل</div>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">0</div>
              <div className="text-sm text-gray-600">مستلزمات مكتبية</div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
