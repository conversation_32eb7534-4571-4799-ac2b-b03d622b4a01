'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import DashboardLayout from '@/components/DashboardLayout';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  UsersIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

export default function CustomersPage() {
  const t = useTranslations();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);

  // Mock data للعملاء
  const customers = [
    {
      id: 1,
      name: 'شركة الأمل للتجارة',
      email: '<EMAIL>',
      phone: '+966501234567',
      address: 'الرياض، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: 15000,
      totalInvoices: 8,
      lastTransaction: '2024-01-15',
      status: 'active'
    },
    {
      id: 2,
      name: 'مؤسسة النور للخدمات',
      email: '<EMAIL>',
      phone: '+966507654321',
      address: 'جدة، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: -2500,
      totalInvoices: 12,
      lastTransaction: '2024-01-12',
      status: 'active'
    },
    {
      id: 3,
      name: 'شركة المستقبل التقنية',
      email: '<EMAIL>',
      phone: '+966551234567',
      address: 'الدمام، المملكة العربية السعودية',
      taxNumber: '*********',
      balance: 8500,
      totalInvoices: 5,
      lastTransaction: '2024-01-10',
      status: 'active'
    }
  ];

  // Mock data لكشف حساب العميل
  const customerTransactions = [
    { date: '2024-01-15', description: 'فاتورة مبيعات #001', debit: 15000, credit: 0, balance: 15000 },
    { date: '2024-01-10', description: 'دفعة نقدية', debit: 0, credit: 5000, balance: 10000 },
    { date: '2024-01-08', description: 'فاتورة مبيعات #002', debit: 8500, credit: 0, balance: 15000 },
    { date: '2024-01-05', description: 'دفعة نقدية', debit: 0, credit: 3500, balance: 6500 }
  ];

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600';
    if (balance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const renderCustomerStatement = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <button
          onClick={() => setSelectedCustomer(null)}
          className="text-gray-500 hover:text-gray-700"
        >
          ← العودة للعملاء
        </button>
        <div className="flex gap-2">
          <button className="btn-secondary">طباعة كشف الحساب</button>
          <button className="btn-primary">إنشاء فاتورة جديدة</button>
        </div>
      </div>

      {/* معلومات العميل */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات العميل</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <UsersIcon className="h-5 w-5 text-gray-400 ml-3" />
                <span className="font-medium">{selectedCustomer.name}</span>
              </div>
              <div className="flex items-center">
                <EnvelopeIcon className="h-5 w-5 text-gray-400 ml-3" />
                <span>{selectedCustomer.email}</span>
              </div>
              <div className="flex items-center">
                <PhoneIcon className="h-5 w-5 text-gray-400 ml-3" />
                <span>{selectedCustomer.phone}</span>
              </div>
              <div className="flex items-center">
                <MapPinIcon className="h-5 w-5 text-gray-400 ml-3" />
                <span>{selectedCustomer.address}</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">ملخص الحساب</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">الرصيد الحالي:</span>
                <span className={`font-bold ${getBalanceColor(selectedCustomer.balance)}`}>
                  {selectedCustomer.balance.toLocaleString('ar-SA')} ر.س
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">عدد الفواتير:</span>
                <span className="font-medium">{selectedCustomer.totalInvoices}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">آخر معاملة:</span>
                <span className="font-medium">{selectedCustomer.lastTransaction}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* كشف الحساب */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">كشف الحساب</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوصف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">مدين</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">دائن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرصيد</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {customerTransactions.map((transaction, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{transaction.description}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.debit > 0 ? `${transaction.debit.toLocaleString('ar-SA')} ر.س` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.credit > 0 ? `${transaction.credit.toLocaleString('ar-SA')} ر.س` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <span className={getBalanceColor(transaction.balance)}>
                      {transaction.balance.toLocaleString('ar-SA')} ر.س
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  if (selectedCustomer) {
    return (
      <DashboardLayout>
        {renderCustomerStatement()}
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">{t('navigation.customers')}</h1>
          <button className="btn-primary flex items-center">
            <PlusIcon className="h-5 w-5 ml-2" />
            إضافة عميل جديد
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في العملاء..."
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <UsersIcon className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي العملاء</p>
                <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي الذمم المدينة</p>
                <p className="text-2xl font-bold text-green-600">
                  {customers.filter(c => c.balance > 0).reduce((sum, c) => sum + c.balance, 0).toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي الذمم الدائنة</p>
                <p className="text-2xl font-bold text-red-600">
                  {Math.abs(customers.filter(c => c.balance < 0).reduce((sum, c) => sum + c.balance, 0)).toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 font-bold text-sm">فعال</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">العملاء النشطون</p>
                <p className="text-2xl font-bold text-purple-600">
                  {customers.filter(c => c.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    اسم العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البريد الإلكتروني
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الهاتف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرصيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عدد الفواتير
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر معاملة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {customers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-primary-600 font-medium text-sm">
                            {customer.name.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.taxNumber}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.phone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <span className={getBalanceColor(customer.balance)}>
                        {customer.balance.toLocaleString('ar-SA')} ر.س
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.totalInvoices}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {customer.lastTransaction}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedCustomer(customer)}
                          className="text-primary-600 hover:text-primary-900"
                          title="كشف الحساب"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900" title="تعديل">
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button className="text-red-600 hover:text-red-900" title="حذف">
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
