'use client';

import { useRouter, usePathname } from 'next/navigation';
import { GlobeAltIcon } from '@heroicons/react/24/outline';

export default function LanguageToggle() {
  const router = useRouter();
  const pathname = usePathname();

  // استخراج اللغة من المسار
  const locale = pathname.startsWith('/ar') ? 'ar' : 'en';

  const toggleLanguage = () => {
    const newLocale = locale === 'ar' ? 'en' : 'ar';
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  return (
    <button
      onClick={toggleLanguage}
      className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
    >
      <GlobeAltIcon className="h-4 w-4" />
      <span>{locale === 'ar' ? 'English' : 'العربية'}</span>
    </button>
  );
}
