# نظام المحاسبة الاحترافي | Professional Accounting System

نظام محاسبي احترافي شبيه بـ Odoo، مصمم خصيصاً للشركات الصغيرة والمتوسطة مع دعم كامل للغة العربية والإنجليزية.

## 🌟 المزايا الرئيسية

### 📊 لوحة تحكم تفاعلية
- عرض شامل للوضع المالي
- رسوم بيانية وإحصائيات مفصلة
- ملخص الإيرادات والمصروفات
- تتبع الفواتير المعلقة والمتأخرة

### 📄 إدارة الفواتير
- إنشاء وإدارة الفواتير الصادرة والواردة
- تتبع حالة المدفوعات
- طباعة وتصدير الفواتير
- حساب الضرائب تلقائياً

### 📚 نظام القيود المحاسبية
- دفتر يومية شامل
- دليل حسابات مرن
- قيود محاسبية دقيقة
- ترحيل القيود

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة
- كشوف حسابات مفصلة
- تتبع الذمم المدينة والدائنة
- تاريخ المعاملات

### 📈 التقارير المالية
- الميزانية العمومية
- قائمة الدخل
- التدفقات النقدية
- ميزان المراجعة
- تقارير شهرية وسنوية

### 💰 إدارة الضرائب
- حساب ضريبة القيمة المضافة
- إقرارات ضريبية
- تتبع الضرائب المحصلة والمدفوعة
- تذكيرات مواعيد التقديم

### 🔐 نظام الصلاحيات
- ثلاثة مستويات: مدير، محاسب، موظف
- حماية الصفحات حسب الدور
- تسجيل دخول آمن

### 🌐 دعم متعدد اللغات
- العربية والإنجليزية
- تبديل سهل بين اللغات
- واجهة مناسبة للغة العربية (RTL)

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Heroicons, Lucide React
- **Internationalization**: next-intl
- **State Management**: React Hooks
- **Authentication**: Custom implementation

## 🚀 التشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn

### التثبيت

```bash
# استنساخ المشروع
git clone [repository-url]
cd accounting-system

# تثبيت المكتبات
npm install

# تشغيل الخادم التطويري
npm run dev
```

### الوصول للنظام
- افتح المتصفح على: `http://localhost:3000`
- سيتم توجيهك تلقائياً للصفحة العربية
- للوصول للإنجليزية: `http://localhost:3000/en`

### بيانات تسجيل الدخول التجريبية
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: أي كلمة مرور

## 📁 هيكل المشروع

```
accounting-system/
├── src/
│   ├── app/
│   │   ├── [locale]/
│   │   │   ├── dashboard/
│   │   │   ├── invoices/
│   │   │   ├── journal-entries/
│   │   │   ├── customers/
│   │   │   ├── suppliers/
│   │   │   ├── reports/
│   │   │   ├── taxes/
│   │   │   ├── settings/
│   │   │   └── login/
│   │   └── globals.css
│   ├── components/
│   │   ├── AuthGuard.tsx
│   │   ├── DashboardLayout.tsx
│   │   ├── Sidebar.tsx
│   │   └── LanguageToggle.tsx
│   ├── types/
│   │   └── index.ts
│   ├── i18n/
│   │   └── request.ts
│   └── lib/
├── messages/
│   ├── ar.json
│   └── en.json
├── middleware.ts
├── next.config.js
├── tailwind.config.js
└── package.json
```

## 🎯 الصفحات والوظائف

### 🏠 لوحة التحكم (`/dashboard`)
- إحصائيات مالية شاملة
- المعاملات الأخيرة
- إجراءات سريعة
- رسوم بيانية

### 📄 الفواتير (`/invoices`)
- الفواتير الصادرة والواردة
- إنشاء فواتير جديدة
- تتبع حالة الدفع
- تصدير وطباعة

### 📚 القيود اليومية (`/journal-entries`)
- عرض جميع القيود
- إنشاء قيود جديدة
- دليل الحسابات
- ترحيل القيود

### 👥 العملاء (`/customers`)
- قائمة العملاء
- كشوف الحسابات
- إضافة عملاء جدد
- تتبع الذمم

### 🚚 الموردين (`/suppliers`)
- قائمة الموردين
- كشوف الحسابات
- تصنيف الموردين
- إدارة المدفوعات

### 📊 التقارير (`/reports`)
- الميزانية العمومية
- قائمة الدخل
- التدفقات النقدية
- تصدير التقارير

### 💰 الضرائب (`/taxes`)
- ضريبة القيمة المضافة
- الإقرارات الضريبية
- تتبع المواعيد
- حساب الضرائب

### ⚙️ الإعدادات (`/settings`)
- معلومات الشركة
- إدارة المستخدمين
- الإعدادات المحاسبية
- إعدادات النظام

## 🔒 نظام الصلاحيات

### مدير (Admin)
- الوصول لجميع الصفحات
- إدارة المستخدمين
- تعديل الإعدادات
- عرض جميع التقارير

### محاسب (Accountant)
- لوحة التحكم
- إدارة الفواتير
- القيود المحاسبية
- التقارير المالية
- إدارة الضرائب
- العملاء والموردين

### موظف (Employee)
- لوحة التحكم
- عرض الفواتير
- العملاء والموردين (عرض فقط)

## 🌐 دعم اللغات

النظام يدعم اللغتين العربية والإنجليزية مع:
- تبديل سهل بين اللغات
- واجهة RTL للعربية
- ترجمة شاملة لجميع النصوص
- تنسيق التواريخ والأرقام حسب اللغة

## 📱 التصميم المتجاوب

- متوافق مع جميع أحجام الشاشات
- تصميم mobile-first
- قوائم تنقل متكيفة
- جداول قابلة للتمرير

## 🔮 التطوير المستقبلي

- [ ] ربط قاعدة بيانات حقيقية
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تطبيق موبايل
- [ ] تقارير متقدمة
- [ ] نظام النسخ الاحتياطي
- [ ] تكامل مع البنوك
- [ ] إشعارات في الوقت الفعلي

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

## 📞 الدعم

للدعم والاستفسارات، يرجى فتح issue في المشروع.

---

**تم تطويره بـ ❤️ للمجتمع العربي**
