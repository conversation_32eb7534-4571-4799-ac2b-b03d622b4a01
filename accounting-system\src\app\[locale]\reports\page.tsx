'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  PrinterIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ScaleIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedReport, setSelectedReport] = useState<string | null>(null);

  // Mock data للتقارير
  const reportTypes = [
    {
      id: 'balance-sheet',
      name: 'الميزانية العمومية',
      description: 'عرض الأصول والخصوم وحقوق الملكية',
      icon: ScaleIcon,
      color: 'bg-blue-500'
    },
    {
      id: 'income-statement',
      name: 'قائمة الدخل',
      description: 'عرض الإيرادات والمصروفات وصافي الربح',
      icon: ArrowTrendingUpIcon,
      color: 'bg-green-500'
    },
    {
      id: 'cash-flow',
      name: 'التدفقات النقدية',
      description: 'عرض التدفقات النقدية الداخلة والخارجة',
      icon: BanknotesIcon,
      color: 'bg-purple-500'
    },
    {
      id: 'trial-balance',
      name: 'ميزان المراجعة',
      description: 'عرض أرصدة جميع الحسابات',
      icon: ChartBarIcon,
      color: 'bg-orange-500'
    }
  ];

  // Mock data للميزانية العمومية
  const balanceSheetData = {
    assets: {
      current: [
        { name: 'النقدية في الصندوق', amount: 25000 },
        { name: 'النقدية في البنك', amount: 150000 },
        { name: 'الذمم المدينة', amount: 85000 },
        { name: 'المخزون', amount: 120000 }
      ],
      fixed: [
        { name: 'الأثاث والمعدات', amount: 200000 },
        { name: 'السيارات', amount: 180000 },
        { name: 'المباني', amount: 500000 }
      ]
    },
    liabilities: {
      current: [
        { name: 'الذمم الدائنة', amount: 45000 },
        { name: 'المصروفات المستحقة', amount: 15000 }
      ],
      longTerm: [
        { name: 'قروض طويلة الأجل', amount: 300000 }
      ]
    },
    equity: [
      { name: 'رأس المال', amount: 500000 },
      { name: 'الأرباح المحتجزة', amount: 400000 }
    ]
  };

  // Mock data لقائمة الدخل
  const incomeStatementData = {
    revenue: [
      { name: 'مبيعات البضائع', amount: 450000 },
      { name: 'إيرادات الخدمات', amount: 125000 },
      { name: 'إيرادات أخرى', amount: 25000 }
    ],
    expenses: [
      { name: 'تكلفة البضاعة المباعة', amount: 280000 },
      { name: 'رواتب الموظفين', amount: 120000 },
      { name: 'إيجار المكتب', amount: 48000 },
      { name: 'مصروفات التسويق', amount: 35000 },
      { name: 'مصروفات عمومية', amount: 42000 }
    ]
  };

  const totalAssets = [
    ...balanceSheetData.assets.current,
    ...balanceSheetData.assets.fixed
  ].reduce((sum, item) => sum + item.amount, 0);

  const totalLiabilities = [
    ...balanceSheetData.liabilities.current,
    ...balanceSheetData.liabilities.longTerm
  ].reduce((sum, item) => sum + item.amount, 0);

  const totalEquity = balanceSheetData.equity.reduce((sum, item) => sum + item.amount, 0);

  const totalRevenue = incomeStatementData.revenue.reduce((sum, item) => sum + item.amount, 0);
  const totalExpenses = incomeStatementData.expenses.reduce((sum, item) => sum + item.amount, 0);
  const netIncome = totalRevenue - totalExpenses;

  const renderBalanceSheet = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* الأصول */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-gray-900">الأصول</h3>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">الأصول المتداولة</h4>
          {balanceSheetData.assets.current.map((asset, index) => (
            <div key={index} className="flex justify-between py-2">
              <span className="text-gray-700">{asset.name}</span>
              <span className="font-medium">{asset.amount.toLocaleString('ar-SA')} ر.س</span>
            </div>
          ))}
          <div className="border-t pt-2 mt-2">
            <div className="flex justify-between font-semibold">
              <span>إجمالي الأصول المتداولة</span>
              <span>{balanceSheetData.assets.current.reduce((sum, item) => sum + item.amount, 0).toLocaleString('ar-SA')} ر.س</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">الأصول الثابتة</h4>
          {balanceSheetData.assets.fixed.map((asset, index) => (
            <div key={index} className="flex justify-between py-2">
              <span className="text-gray-700">{asset.name}</span>
              <span className="font-medium">{asset.amount.toLocaleString('ar-SA')} ر.س</span>
            </div>
          ))}
          <div className="border-t pt-2 mt-2">
            <div className="flex justify-between font-semibold">
              <span>إجمالي الأصول الثابتة</span>
              <span>{balanceSheetData.assets.fixed.reduce((sum, item) => sum + item.amount, 0).toLocaleString('ar-SA')} ر.س</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex justify-between text-lg font-bold text-blue-900">
            <span>إجمالي الأصول</span>
            <span>{totalAssets.toLocaleString('ar-SA')} ر.س</span>
          </div>
        </div>
      </div>

      {/* الخصوم وحقوق الملكية */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-gray-900">الخصوم وحقوق الملكية</h3>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">الخصوم المتداولة</h4>
          {balanceSheetData.liabilities.current.map((liability, index) => (
            <div key={index} className="flex justify-between py-2">
              <span className="text-gray-700">{liability.name}</span>
              <span className="font-medium">{liability.amount.toLocaleString('ar-SA')} ر.س</span>
            </div>
          ))}
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">الخصوم طويلة الأجل</h4>
          {balanceSheetData.liabilities.longTerm.map((liability, index) => (
            <div key={index} className="flex justify-between py-2">
              <span className="text-gray-700">{liability.name}</span>
              <span className="font-medium">{liability.amount.toLocaleString('ar-SA')} ر.س</span>
            </div>
          ))}
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">حقوق الملكية</h4>
          {balanceSheetData.equity.map((equity, index) => (
            <div key={index} className="flex justify-between py-2">
              <span className="text-gray-700">{equity.name}</span>
              <span className="font-medium">{equity.amount.toLocaleString('ar-SA')} ر.س</span>
            </div>
          ))}
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex justify-between text-lg font-bold text-blue-900">
            <span>إجمالي الخصوم وحقوق الملكية</span>
            <span>{(totalLiabilities + totalEquity).toLocaleString('ar-SA')} ر.س</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIncomeStatement = () => (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-800 mb-4">الإيرادات</h4>
        {incomeStatementData.revenue.map((item, index) => (
          <div key={index} className="flex justify-between py-2">
            <span className="text-gray-700">{item.name}</span>
            <span className="font-medium">{item.amount.toLocaleString('ar-SA')} ر.س</span>
          </div>
        ))}
        <div className="border-t pt-2 mt-2">
          <div className="flex justify-between font-semibold text-green-700">
            <span>إجمالي الإيرادات</span>
            <span>{totalRevenue.toLocaleString('ar-SA')} ر.س</span>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-800 mb-4">المصروفات</h4>
        {incomeStatementData.expenses.map((item, index) => (
          <div key={index} className="flex justify-between py-2">
            <span className="text-gray-700">{item.name}</span>
            <span className="font-medium">({item.amount.toLocaleString('ar-SA')}) ر.س</span>
          </div>
        ))}
        <div className="border-t pt-2 mt-2">
          <div className="flex justify-between font-semibold text-red-700">
            <span>إجمالي المصروفات</span>
            <span>({totalExpenses.toLocaleString('ar-SA')}) ر.س</span>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-6">
        <div className="flex justify-between text-xl font-bold text-blue-900">
          <span>صافي الربح</span>
          <span className={netIncome >= 0 ? 'text-green-700' : 'text-red-700'}>
            {netIncome.toLocaleString('ar-SA')} ر.س
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout requiredRole="accountant">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">التقارير المالية</h1>
          <div className="flex gap-2">
            <select
              className="input-field"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <option value="current-month">الشهر الحالي</option>
              <option value="last-month">الشهر الماضي</option>
              <option value="current-quarter">الربع الحالي</option>
              <option value="current-year">السنة الحالية</option>
              <option value="custom">فترة مخصصة</option>
            </select>
          </div>
        </div>

        {!selectedReport ? (
          <>
            {/* Report Types Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {reportTypes.map((report) => {
                const Icon = report.icon;
                return (
                  <div
                    key={report.id}
                    onClick={() => setSelectedReport(report.id)}
                    className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                  >
                    <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${report.color} mb-4`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{report.name}</h3>
                    <p className="text-sm text-gray-500">{report.description}</p>
                  </div>
                );
              })}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-green-600">
                      {totalRevenue.toLocaleString('ar-SA')} ر.س
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <ArrowTrendingUpIcon className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">صافي الربح</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {netIncome.toLocaleString('ar-SA')} ر.س
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <ScaleIcon className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">إجمالي الأصول</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {totalAssets.toLocaleString('ar-SA')} ر.س
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Report Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  onClick={() => setSelectedReport(null)}
                  className="text-gray-500 hover:text-gray-700 ml-4"
                >
                  ← العودة
                </button>
                <h2 className="text-xl font-bold text-gray-900">
                  {reportTypes.find(r => r.id === selectedReport)?.name}
                </h2>
              </div>
              <div className="flex gap-2">
                <button className="btn-secondary flex items-center">
                  <PrinterIcon className="h-5 w-5 ml-2" />
                  {t('common.print')}
                </button>
                <button className="btn-secondary flex items-center">
                  <DocumentArrowDownIcon className="h-5 w-5 ml-2" />
                  {t('common.export')}
                </button>
              </div>
            </div>

            {/* Report Content */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900">
                  {reportTypes.find(r => r.id === selectedReport)?.name}
                </h3>
                <p className="text-gray-500 mt-2">
                  للفترة: {selectedPeriod === 'current-month' ? 'يناير 2024' : 'الفترة المحددة'}
                </p>
              </div>

              {selectedReport === 'balance-sheet' && renderBalanceSheet()}
              {selectedReport === 'income-statement' && renderIncomeStatement()}
              {selectedReport === 'cash-flow' && (
                <div className="text-center py-12">
                  <p className="text-gray-500">تقرير التدفقات النقدية قيد التطوير</p>
                </div>
              )}
              {selectedReport === 'trial-balance' && (
                <div className="text-center py-12">
                  <p className="text-gray-500">ميزان المراجعة قيد التطوير</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
}
