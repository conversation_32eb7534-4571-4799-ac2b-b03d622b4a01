import { ReactNode } from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'indigo';
  onClick?: () => void;
}

export default function StatCard({ 
  title, 
  value, 
  icon, 
  change, 
  color = 'blue',
  onClick 
}: StatCardProps) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    red: 'bg-red-100 text-red-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    purple: 'bg-purple-100 text-purple-600',
    indigo: 'bg-indigo-100 text-indigo-600'
  };

  const valueColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    indigo: 'text-indigo-600'
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${
        onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-center">
        <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className={`text-2xl font-bold ${valueColorClasses[color]}`}>
            {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}
          </p>
          {change && (
            <div className="flex items-center mt-1">
              <span
                className={`text-xs font-medium ${
                  change.type === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              <span className="text-xs text-gray-500 mr-2">
                {change.period}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// مكون للإحصائيات المالية
interface FinancialStatCardProps {
  title: string;
  amount: number;
  currency?: string;
  icon: ReactNode;
  trend?: {
    value: number;
    period: string;
  };
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'indigo';
}

export function FinancialStatCard({ 
  title, 
  amount, 
  currency = 'ر.س',
  icon, 
  trend,
  color = 'blue' 
}: FinancialStatCardProps) {
  const formattedAmount = `${amount.toLocaleString('ar-SA')} ${currency}`;
  
  const change = trend ? {
    value: trend.value,
    type: trend.value >= 0 ? 'increase' as const : 'decrease' as const,
    period: trend.period
  } : undefined;

  return (
    <StatCard
      title={title}
      value={formattedAmount}
      icon={icon}
      change={change}
      color={color}
    />
  );
}

// مكون للإحصائيات العددية
interface CountStatCardProps {
  title: string;
  count: number;
  icon: ReactNode;
  subtitle?: string;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'indigo';
  onClick?: () => void;
}

export function CountStatCard({ 
  title, 
  count, 
  icon, 
  subtitle,
  color = 'blue',
  onClick 
}: CountStatCardProps) {
  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${
        onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{count.toLocaleString('ar-SA')}</p>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${
          color === 'blue' ? 'bg-blue-100 text-blue-600' :
          color === 'green' ? 'bg-green-100 text-green-600' :
          color === 'red' ? 'bg-red-100 text-red-600' :
          color === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
          color === 'purple' ? 'bg-purple-100 text-purple-600' :
          'bg-indigo-100 text-indigo-600'
        }`}>
          {icon}
        </div>
      </div>
    </div>
  );
}
