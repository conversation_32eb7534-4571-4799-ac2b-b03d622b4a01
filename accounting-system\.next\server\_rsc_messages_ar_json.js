"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_ar_json";
exports.ids = ["_rsc_messages_ar_json"];
exports.modules = {

/***/ "(rsc)/./messages/ar.json":
/*!**************************!*\
  !*** ./messages/ar.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"common":{"save":"حفظ","cancel":"إلغاء","edit":"تعديل","delete":"حذف","add":"إضافة","search":"بحث","filter":"تصفية","export":"تصدير","print":"طباعة","loading":"جاري التحميل...","noData":"لا توجد بيانات","confirm":"تأكيد","yes":"نعم","no":"لا"},"navigation":{"dashboard":"لوحة التحكم","invoices":"الفواتير","journalEntries":"القيود اليومية","customers":"العملاء","suppliers":"الموردين","reports":"التقارير","taxes":"الضرائب","settings":"الإعدادات","logout":"تسجيل الخروج"},"auth":{"login":"تسجيل الدخول","email":"البريد الإلكتروني","password":"كلمة المرور","rememberMe":"تذكرني","forgotPassword":"نسيت كلمة المرور؟","loginButton":"دخول","invalidCredentials":"بيانات الدخول غير صحيحة"},"dashboard":{"title":"لوحة التحكم","totalRevenue":"إجمالي الإيرادات","totalExpenses":"إجمالي المصروفات","netProfit":"صافي الربح","pendingInvoices":"الفواتير المعلقة","recentTransactions":"المعاملات الأخيرة","monthlyChart":"الرسم البياني الشهري","quickActions":"الإجراءات السريعة"},"invoices":{"title":"إدارة الفواتير","outgoing":"الفواتير الصادرة","incoming":"الفواتير الواردة","createInvoice":"إنشاء فاتورة جديدة","invoiceNumber":"رقم الفاتورة","date":"التاريخ","customer":"العميل","amount":"المبلغ","status":"الحالة","paid":"مدفوعة","pending":"معلقة","overdue":"متأخرة"},"reports":{"title":"التقارير المالية","balanceSheet":"الميزانية العمومية","incomeStatement":"قائمة الدخل","cashFlow":"التدفقات النقدية","trialBalance":"ميزان المراجعة","monthlyReport":"التقرير الشهري","yearlyReport":"التقرير السنوي"}}');

/***/ })

};
;