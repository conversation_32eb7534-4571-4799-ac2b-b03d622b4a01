{"c": ["app/[locale]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js", "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js", "(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5C%D9%85%D9%83%D8%AA%D8%A8%D8%A9%20%D8%A7%D8%B5%D9%88%D9%84%20%D8%A7%D9%84%D8%A7%D8%A8%D8%AA%D9%83%D8%A7%D8%B1%5C%5CDocuments%5C%5Cuuuuuu%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5C%D9%85%D9%83%D8%AA%D8%A8%D8%A9%20%D8%A7%D8%B5%D9%88%D9%84%20%D8%A7%D9%84%D8%A7%D8%A8%D8%AA%D9%83%D8%A7%D8%B1%5C%5CDocuments%5C%5Cuuuuuu%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5C%D9%85%D9%83%D8%AA%D8%A8%D8%A9%20%D8%A7%D8%B5%D9%88%D9%84%20%D8%A7%D9%84%D8%A7%D8%A8%D8%AA%D9%83%D8%A7%D8%B1%5C%5CDocuments%5C%5Cuuuuuu%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CLanguageToggle.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-DPFnvsUO.js", "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js", "(app-pages-browser)/./src/components/LanguageToggle.tsx"]}