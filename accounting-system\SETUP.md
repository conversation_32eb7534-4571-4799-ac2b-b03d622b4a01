# دليل التشغيل السريع | Quick Setup Guide

## 🚀 التشغيل السريع

### 1. تشغيل النظام
```bash
# الانتقال لمجلد المشروع
cd accounting-system

# تشغيل الخادم التطويري
npm run dev
```

### 2. الوصول للنظام
- افتح المتصفح على: `http://localhost:3000`
- سيتم توجيهك تلقائياً للصفحة العربية
- للوصول للإنجليزية: `http://localhost:3000/en`

### 3. تسجيل الدخول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: أي كلمة مرور (النظام تجريبي)

## 📋 الصفحات المتاحة

### 🏠 الصفحة الرئيسية
- `http://localhost:3000/ar` - الصفحة الرئيسية بالعربية
- `http://localhost:3000/en` - الصفحة الرئيسية بالإنجليزية

### 🔐 تسجيل الدخول
- `http://localhost:3000/ar/login` - صفحة تسجيل الدخول

### 📊 لوحة التحكم
- `http://localhost:3000/ar/dashboard` - لوحة التحكم الرئيسية

### 📄 الفواتير
- `http://localhost:3000/ar/invoices` - إدارة الفواتير

### 📚 القيود المحاسبية
- `http://localhost:3000/ar/journal-entries` - دفتر اليومية

### 👥 العملاء
- `http://localhost:3000/ar/customers` - إدارة العملاء

### 🚚 الموردين
- `http://localhost:3000/ar/suppliers` - إدارة الموردين

### 📈 التقارير
- `http://localhost:3000/ar/reports` - التقارير المالية

### 💰 الضرائب
- `http://localhost:3000/ar/taxes` - إدارة الضرائب

### ⚙️ الإعدادات
- `http://localhost:3000/ar/settings` - إعدادات النظام

## 🔑 نظام الصلاحيات

### مدير (Admin)
- الوصول لجميع الصفحات
- إدارة المستخدمين والإعدادات

### محاسب (Accountant)
- لوحة التحكم
- إدارة الفواتير والقيود
- التقارير المالية
- إدارة الضرائب
- العملاء والموردين

### موظف (Employee)
- لوحة التحكم (عرض فقط)
- عرض الفواتير
- عرض العملاء والموردين

## 🎨 المزايا المتاحة

### ✅ تم التطوير
- [x] لوحة تحكم تفاعلية
- [x] إدارة الفواتير (صادرة/واردة)
- [x] نظام القيود المحاسبية
- [x] إدارة العملاء والموردين
- [x] التقارير المالية (ميزانية، قائمة دخل)
- [x] إدارة الضرائب (ضريبة القيمة المضافة)
- [x] نظام الصلاحيات (3 مستويات)
- [x] دعم اللغة العربية والإنجليزية
- [x] تصميم متجاوب
- [x] واجهة RTL للعربية

### 🔧 مكونات إضافية
- [x] نظام التنقل (Breadcrumb)
- [x] مكونات التحميل (Loading)
- [x] الإشعارات (Toast)
- [x] النوافذ المنبثقة (Modal)
- [x] الجداول التفاعلية (DataTable)
- [x] بطاقات الإحصائيات (StatCard)

## 🛠️ التطوير المستقبلي

### قيد التطوير
- [ ] ربط قاعدة بيانات حقيقية
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تقارير متقدمة (التدفقات النقدية، ميزان المراجعة)
- [ ] نظام النسخ الاحتياطي
- [ ] إشعارات في الوقت الفعلي
- [ ] تطبيق موبايل

## 📱 التصميم المتجاوب

النظام متوافق مع:
- 💻 أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الأجهزة اللوحية
- 📱 الهواتف الذكية

## 🌐 دعم اللغات

- 🇸🇦 العربية (افتراضي)
- 🇺🇸 الإنجليزية
- تبديل سهل بين اللغات
- واجهة RTL للعربية

## 🔧 استكشاف الأخطاء

### مشكلة: الخادم لا يعمل
```bash
# تأكد من تثبيت المكتبات
npm install

# تشغيل الخادم
npm run dev
```

### مشكلة: صفحة فارغة
- تأكد من أن الخادم يعمل على المنفذ 3000
- تحقق من وجود أخطاء في وحدة التحكم

### مشكلة: لا يمكن تسجيل الدخول
- استخدم أي بريد إلكتروني وكلمة مرور
- النظام تجريبي ولا يتطلب بيانات صحيحة

## 📞 الدعم

للمساعدة والاستفسارات:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من التوثيق في المجلدات
- افتح issue في المشروع للمشاكل التقنية

---

**تم تطويره بـ ❤️ للمجتمع العربي**
