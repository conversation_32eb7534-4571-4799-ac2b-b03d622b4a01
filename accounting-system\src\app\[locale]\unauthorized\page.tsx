import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="mx-auto h-24 w-24 bg-red-100 rounded-full flex items-center justify-center mb-6">
            <svg className="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">غير مخول</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">ليس لديك صلاحية للوصول</h2>
          <p className="text-gray-600 mb-8">
            عذراً، ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة. يرجى التواصل مع المدير لطلب الصلاحيات المناسبة.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/ar/dashboard"
            className="block w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
          >
            العودة للوحة التحكم
          </Link>
          <button
            onClick={() => {
              localStorage.removeItem('user');
              window.location.href = '/ar/login';
            }}
            className="block w-full bg-white hover:bg-gray-50 text-gray-900 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200"
          >
            تسجيل الخروج
          </button>
        </div>
        
        <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h3 className="text-sm font-medium text-yellow-800 mb-2">مستويات الصلاحيات:</h3>
          <ul className="text-xs text-yellow-700 space-y-1">
            <li><strong>مدير:</strong> الوصول الكامل لجميع الوظائف</li>
            <li><strong>محاسب:</strong> إدارة الحسابات والتقارير</li>
            <li><strong>موظف:</strong> عرض البيانات الأساسية فقط</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
