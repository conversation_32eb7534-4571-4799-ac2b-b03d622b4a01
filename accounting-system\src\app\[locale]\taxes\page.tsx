'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import {
  PlusIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

export default function TaxesPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('current-quarter');
  const [activeTab, setActiveTab] = useState<'overview' | 'vat' | 'returns'>('overview');

  // Mock data للضرائب
  const taxData = {
    vat: {
      rate: 15, // ضريبة القيمة المضافة 15%
      collected: 45000, // الضريبة المحصلة من المبيعات
      paid: 18000, // الضريبة المدفوعة على المشتريات
      netDue: 27000, // صافي الضريبة المستحقة
      lastReturn: '2024-01-31',
      nextDue: '2024-04-30'
    },
    transactions: [
      {
        id: 1,
        date: '2024-01-15',
        type: 'sale',
        description: 'فاتورة مبيعات #001',
        amount: 100000,
        vatAmount: 15000,
        status: 'collected'
      },
      {
        id: 2,
        date: '2024-01-14',
        type: 'purchase',
        description: 'فاتورة مشتريات #001',
        amount: 50000,
        vatAmount: 7500,
        status: 'paid'
      },
      {
        id: 3,
        date: '2024-01-12',
        type: 'sale',
        description: 'فاتورة مبيعات #002',
        amount: 80000,
        vatAmount: 12000,
        status: 'collected'
      },
      {
        id: 4,
        date: '2024-01-10',
        type: 'purchase',
        description: 'فاتورة مشتريات #002',
        amount: 30000,
        vatAmount: 4500,
        status: 'paid'
      }
    ],
    returns: [
      {
        id: 1,
        period: 'Q4 2023',
        dueDate: '2024-01-31',
        status: 'submitted',
        vatDue: 25000,
        submittedDate: '2024-01-28'
      },
      {
        id: 2,
        period: 'Q3 2023',
        dueDate: '2023-10-31',
        status: 'paid',
        vatDue: 32000,
        submittedDate: '2023-10-25'
      },
      {
        id: 3,
        period: 'Q1 2024',
        dueDate: '2024-04-30',
        status: 'pending',
        vatDue: 27000,
        submittedDate: null
      }
    ]
  };

  const getTransactionIcon = (type: string) => {
    return type === 'sale' ? (
      <ArrowUpIcon className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownIcon className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'مقدم';
      case 'paid':
        return 'مدفوع';
      case 'pending':
        return 'معلق';
      case 'overdue':
        return 'متأخر';
      default:
        return status;
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <ArrowUpIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">ضريبة محصلة</p>
              <p className="text-2xl font-bold text-green-600">
                {taxData.vat.collected.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
              <ArrowDownIcon className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">ضريبة مدفوعة</p>
              <p className="text-2xl font-bold text-red-600">
                {taxData.vat.paid.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">صافي المستحق</p>
              <p className="text-2xl font-bold text-blue-600">
                {taxData.vat.netDue.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <CalendarIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">موعد التقديم القادم</p>
              <p className="text-lg font-bold text-yellow-600">{taxData.vat.nextDue}</p>
            </div>
          </div>
        </div>
      </div>

      {/* VAT Rate Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات ضريبة القيمة المضافة</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              معدل ضريبة القيمة المضافة
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={taxData.vat.rate}
                className="input-field w-24"
                readOnly
              />
              <span className="ml-2 text-gray-500">%</span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الرقم الضريبي للشركة
            </label>
            <input
              type="text"
              value="300123456789003"
              className="input-field"
              readOnly
            />
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">المعاملات الضريبية الأخيرة</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوصف</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الضريبة</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {taxData.transactions.slice(0, 5).map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getTransactionIcon(transaction.type)}
                      <span className="ml-2 text-sm text-gray-900">
                        {transaction.type === 'sale' ? 'مبيعات' : 'مشتريات'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {transaction.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.amount.toLocaleString('ar-SA')} ر.س
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <span className={transaction.type === 'sale' ? 'text-green-600' : 'text-red-600'}>
                      {transaction.vatAmount.toLocaleString('ar-SA')} ر.س
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderVATReturns = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">إقرارات ضريبة القيمة المضافة</h3>
        <button className="btn-primary flex items-center">
          <PlusIcon className="h-5 w-5 ml-2" />
          إنشاء إقرار جديد
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفترة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">موعد التقديم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الضريبة المستحقة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التقديم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {taxData.returns.map((returnItem) => (
                <tr key={returnItem.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {returnItem.period}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {returnItem.dueDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {returnItem.vatDue.toLocaleString('ar-SA')} ر.س
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {returnItem.submittedDate || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(returnItem.status)}`}>
                      {getStatusText(returnItem.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-primary-600 hover:text-primary-900">
                        عرض
                      </button>
                      {returnItem.status === 'pending' && (
                        <button className="text-green-600 hover:text-green-900">
                          تقديم
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Next Return Alert */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 ml-3" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800">
              تذكير: موعد تقديم الإقرار القادم
            </h4>
            <p className="text-sm text-yellow-700 mt-1">
              يجب تقديم إقرار ضريبة القيمة المضافة للربع الأول من 2024 قبل {taxData.vat.nextDue}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout requiredRole="accountant">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">الضرائب</h1>
          <div className="flex gap-2">
            <select
              className="input-field"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <option value="current-quarter">الربع الحالي</option>
              <option value="last-quarter">الربع الماضي</option>
              <option value="current-year">السنة الحالية</option>
              <option value="custom">فترة مخصصة</option>
            </select>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              نظرة عامة
            </button>
            <button
              onClick={() => setActiveTab('vat')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'vat'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              ضريبة القيمة المضافة
            </button>
            <button
              onClick={() => setActiveTab('returns')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'returns'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              الإقرارات الضريبية
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'vat' && renderOverview()}
        {activeTab === 'returns' && renderVATReturns()}
      </div>
    </DashboardLayout>
  );
}
