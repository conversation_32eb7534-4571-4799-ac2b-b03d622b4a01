'use client';

import DashboardLayout from '@/components/DashboardLayout';
import {
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  DocumentTextIcon,
  UsersIcon,
  TruckIcon,
  ExclamationTriangleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function DashboardPage() {

  // Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API
  const stats = {
    totalRevenue: 125000,
    totalExpenses: 85000,
    netProfit: 40000,
    pendingInvoices: 12,
    overdueInvoices: 3,
    totalCustomers: 45,
    totalSuppliers: 23
  };

  const recentTransactions = [
    { id: 1, description: 'فاتورة مبيعات #001', amount: 5000, type: 'income', date: '2024-01-15' },
    { id: 2, description: 'مشتريات مكتبية', amount: -1200, type: 'expense', date: '2024-01-14' },
    { id: 3, description: 'فاتورة مبيعات #002', amount: 3500, type: 'income', date: '2024-01-14' },
    { id: 4, description: 'إيجار المكتب', amount: -8000, type: 'expense', date: '2024-01-13' },
    { id: 5, description: 'فاتورة مبيعات #003', amount: 2800, type: 'income', date: '2024-01-12' }
  ];

  const quickActions = [
    { name: 'إنشاء فاتورة جديدة', href: '/invoices/new', icon: DocumentTextIcon, color: 'bg-blue-500' },
    { name: 'إضافة عميل جديد', href: '/customers/new', icon: UsersIcon, color: 'bg-green-500' },
    { name: 'إضافة مورد جديد', href: '/suppliers/new', icon: TruckIcon, color: 'bg-purple-500' },
    { name: 'إنشاء قيد يومية', href: '/journal-entries/new', icon: PlusIcon, color: 'bg-orange-500' }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
          <div className="text-sm text-gray-500">
            آخر تحديث: {new Date().toLocaleDateString('ar-SA')}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Revenue */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <ArrowTrendingUpIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalRevenue.toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>

          {/* Total Expenses */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <ArrowTrendingDownIcon className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('dashboard.totalExpenses')}</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalExpenses.toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>

          {/* Net Profit */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('dashboard.netProfit')}</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.netProfit.toLocaleString('ar-SA')} ر.س
                </p>
              </div>
            </div>
          </div>

          {/* Pending Invoices */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('dashboard.pendingInvoices')}</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingInvoices}</p>
                {stats.overdueInvoices > 0 && (
                  <p className="text-sm text-red-600 flex items-center mt-1">
                    <ExclamationTriangleIcon className="h-4 w-4 ml-1" />
                    {stats.overdueInvoices} متأخرة
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Transactions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">{t('dashboard.recentTransactions')}</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        transaction.type === 'income' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        {transaction.type === 'income' ? (
                          <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
                        ) : (
                          <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{transaction.description}</p>
                        <p className="text-xs text-gray-500">{transaction.date}</p>
                      </div>
                    </div>
                    <div className={`text-sm font-medium ${
                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{transaction.amount.toLocaleString('ar-SA')} ر.س
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">{t('dashboard.quickActions')}</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <a
                      key={action.name}
                      href={action.href}
                      className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${action.color}`}>
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      <span className="mt-2 text-sm font-medium text-gray-900 text-center">
                        {action.name}
                      </span>
                    </a>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">إجمالي العملاء</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
              </div>
              <UsersIcon className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">إجمالي الموردين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalSuppliers}</p>
              </div>
              <TruckIcon className="h-8 w-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">معدل النمو الشهري</p>
                <p className="text-2xl font-bold text-green-600">+12.5%</p>
              </div>
              <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
