'use client';

import Link from 'next/link';
import { ChevronLeftIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  showHome?: boolean;
}

export default function Breadcrumb({ items, showHome = true }: BreadcrumbProps) {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-4">
        {showHome && (
          <li>
            <div>
              <Link
                href="/ar/dashboard"
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <HomeIcon className="h-5 w-5" />
                <span className="sr-only">الرئيسية</span>
              </Link>
            </div>
          </li>
        )}
        
        {items.map((item, index) => (
          <li key={index}>
            <div className="flex items-center">
              {(showHome || index > 0) && (
                <ChevronLeftIcon className="h-5 w-5 text-gray-400 mx-2" />
              )}
              {item.href && !item.current ? (
                <Link
                  href={item.href}
                  className="text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
                >
                  {item.label}
                </Link>
              ) : (
                <span
                  className={`text-sm font-medium ${
                    item.current 
                      ? 'text-gray-900' 
                      : 'text-gray-500'
                  }`}
                  aria-current={item.current ? 'page' : undefined}
                >
                  {item.label}
                </span>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Hook لإنشاء breadcrumb تلقائياً من المسار
export function useBreadcrumb(pathname: string) {
  const pathSegments = pathname.split('/').filter(Boolean);
  
  // إزالة locale من المسار
  if (pathSegments[0] === 'ar' || pathSegments[0] === 'en') {
    pathSegments.shift();
  }

  const breadcrumbMap: Record<string, string> = {
    'dashboard': 'لوحة التحكم',
    'invoices': 'الفواتير',
    'journal-entries': 'القيود اليومية',
    'customers': 'العملاء',
    'suppliers': 'الموردين',
    'reports': 'التقارير',
    'taxes': 'الضرائب',
    'settings': 'الإعدادات',
    'login': 'تسجيل الدخول',
    'new': 'جديد',
    'edit': 'تعديل',
    'view': 'عرض'
  };

  const items: BreadcrumbItem[] = pathSegments.map((segment, index) => {
    const isLast = index === pathSegments.length - 1;
    const href = isLast ? undefined : `/ar/${pathSegments.slice(0, index + 1).join('/')}`;
    
    return {
      label: breadcrumbMap[segment] || segment,
      href,
      current: isLast
    };
  });

  return items;
}
