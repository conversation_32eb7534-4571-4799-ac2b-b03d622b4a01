'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

export default function JournalEntriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');

  // Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API
  const journalEntries = [
    {
      id: 1,
      entryNumber: 'JE-001',
      date: '2024-01-15',
      description: 'فاتورة مبيعات #001',
      reference: 'INV-001',
      totalDebit: 15000,
      totalCredit: 15000,
      status: 'posted',
      lines: [
        { account: 'الذمم المدينة', debit: 15000, credit: 0 },
        { account: 'المبيعات', debit: 0, credit: 15000 }
      ]
    },
    {
      id: 2,
      entryNumber: 'JE-002',
      date: '2024-01-14',
      description: 'مشتريات مكتبية',
      reference: 'BILL-001',
      totalDebit: 1200,
      totalCredit: 1200,
      status: 'posted',
      lines: [
        { account: 'مصروفات مكتبية', debit: 1200, credit: 0 },
        { account: 'النقدية', debit: 0, credit: 1200 }
      ]
    },
    {
      id: 3,
      entryNumber: 'JE-003',
      date: '2024-01-13',
      description: 'إيجار المكتب - يناير',
      reference: 'RENT-001',
      totalDebit: 8000,
      totalCredit: 8000,
      status: 'draft',
      lines: [
        { account: 'مصروف الإيجار', debit: 8000, credit: 0 },
        { account: 'النقدية', debit: 0, credit: 8000 }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'posted':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'posted':
        return 'مرحل';
      case 'draft':
        return 'مسودة';
      default:
        return status;
    }
  };

  return (
    <DashboardLayout requiredRole="accountant">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">القيود اليومية</h1>
          <button className="btn-primary flex items-center">
            <PlusIcon className="h-5 w-5 ml-2" />
            إنشاء قيد جديد
          </button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في القيود..."
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              className="input-field"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <option value="current-month">الشهر الحالي</option>
              <option value="last-month">الشهر الماضي</option>
              <option value="current-year">السنة الحالية</option>
              <option value="custom">فترة مخصصة</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي القيود</p>
                <p className="text-2xl font-bold text-gray-900">{journalEntries.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">القيود المرحلة</p>
                <p className="text-2xl font-bold text-green-600">
                  {journalEntries.filter(entry => entry.status === 'posted').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">المسودات</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {journalEntries.filter(entry => entry.status === 'draft').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 font-bold">ر.س</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">إجمالي المدين</p>
                <p className="text-2xl font-bold text-purple-600">
                  {journalEntries.reduce((sum, entry) => sum + entry.totalDebit, 0).toLocaleString('ar-SA')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Journal Entries Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    رقم القيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المرجع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المدين
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الدائن
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {journalEntries.map((entry) => (
                  <tr key={entry.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {entry.entryNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {entry.date}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {entry.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {entry.reference}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.totalDebit.toLocaleString('ar-SA')} ر.س
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.totalCredit.toLocaleString('ar-SA')} ر.س
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(entry.status)}`}>
                        {getStatusText(entry.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button className="text-primary-600 hover:text-primary-900" title="عرض">
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        {entry.status === 'draft' && (
                          <button className="text-gray-600 hover:text-gray-900" title="تعديل">
                            <PencilIcon className="h-5 w-5" />
                          </button>
                        )}
                        {entry.status === 'draft' && (
                          <button className="text-red-600 hover:text-red-900" title="حذف">
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Chart of Accounts Quick Access */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">دليل الحسابات</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <h4 className="font-medium text-gray-900">الأصول</h4>
              <p className="text-sm text-gray-500">النقدية، الذمم المدينة، المخزون</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <h4 className="font-medium text-gray-900">الخصوم</h4>
              <p className="text-sm text-gray-500">الذمم الدائنة، القروض</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <h4 className="font-medium text-gray-900">الإيرادات</h4>
              <p className="text-sm text-gray-500">المبيعات، الإيرادات الأخرى</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <h4 className="font-medium text-gray-900">المصروفات</h4>
              <p className="text-sm text-gray-500">تكلفة البضاعة، المصروفات العمومية</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
