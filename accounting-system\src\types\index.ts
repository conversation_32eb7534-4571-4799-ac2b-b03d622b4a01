// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type UserRole = 'admin' | 'accountant' | 'employee';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Invoice Types
export interface Invoice {
  id: string;
  invoiceNumber: string;
  type: 'outgoing' | 'incoming';
  customerId?: string;
  supplierId?: string;
  date: Date;
  dueDate: Date;
  items: InvoiceItem[];
  subtotal: number;
  taxAmount: number;
  total: number;
  status: InvoiceStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  taxRate: number;
}

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';

// Customer and Supplier Types
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  taxNumber?: string;
  balance: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Supplier {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  taxNumber?: string;
  balance: number;
  createdAt: Date;
  updatedAt: Date;
}

// Journal Entry Types
export interface JournalEntry {
  id: string;
  entryNumber: string;
  date: Date;
  description: string;
  reference?: string;
  lines: JournalLine[];
  totalDebit: number;
  totalCredit: number;
  status: 'draft' | 'posted';
  createdAt: Date;
  updatedAt: Date;
}

export interface JournalLine {
  id: string;
  accountId: string;
  account: Account;
  description: string;
  debit: number;
  credit: number;
}

// Chart of Accounts Types
export interface Account {
  id: string;
  code: string;
  name: string;
  type: AccountType;
  parentId?: string;
  children?: Account[];
  balance: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type AccountType = 
  | 'asset' 
  | 'liability' 
  | 'equity' 
  | 'revenue' 
  | 'expense';

// Tax Types
export interface Tax {
  id: string;
  name: string;
  rate: number;
  type: 'vat' | 'sales' | 'income' | 'other';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Report Types
export interface FinancialReport {
  id: string;
  type: ReportType;
  period: ReportPeriod;
  data: any;
  generatedAt: Date;
}

export type ReportType = 
  | 'balance_sheet' 
  | 'income_statement' 
  | 'cash_flow' 
  | 'trial_balance';

export interface ReportPeriod {
  startDate: Date;
  endDate: Date;
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom';
}

// Dashboard Types
export interface DashboardStats {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  pendingInvoices: number;
  overdueInvoices: number;
  totalCustomers: number;
  totalSuppliers: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// Language and Locale Types
export type Locale = 'ar' | 'en';

export interface LocaleConfig {
  locale: Locale;
  direction: 'ltr' | 'rtl';
  currency: string;
  dateFormat: string;
}
