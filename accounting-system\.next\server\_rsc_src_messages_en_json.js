"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_messages_en_json";
exports.ids = ["_rsc_src_messages_en_json"];
exports.modules = {

/***/ "(rsc)/./src/messages/en.json":
/*!******************************!*\
  !*** ./src/messages/en.json ***!
  \******************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"common":{"save":"Save","cancel":"Cancel","edit":"Edit","delete":"Delete","add":"Add","search":"Search","filter":"Filter","export":"Export","print":"Print","loading":"Loading...","noData":"No data available","confirm":"Confirm","yes":"Yes","no":"No"},"navigation":{"dashboard":"Dashboard","invoices":"Invoices","journalEntries":"Journal Entries","customers":"Customers","suppliers":"Suppliers","reports":"Reports","taxes":"Taxes","settings":"Settings","logout":"Logout"},"auth":{"login":"Login","email":"Email","password":"Password","rememberMe":"Remember me","forgotPassword":"Forgot password?","loginButton":"Sign In","invalidCredentials":"Invalid credentials"},"dashboard":{"title":"Dashboard","totalRevenue":"Total Revenue","totalExpenses":"Total Expenses","netProfit":"Net Profit","pendingInvoices":"Pending Invoices","recentTransactions":"Recent Transactions","monthlyChart":"Monthly Chart","quickActions":"Quick Actions"},"invoices":{"title":"Invoice Management","outgoing":"Outgoing Invoices","incoming":"Incoming Invoices","createInvoice":"Create New Invoice","invoiceNumber":"Invoice Number","date":"Date","customer":"Customer","amount":"Amount","status":"Status","paid":"Paid","pending":"Pending","overdue":"Overdue"},"reports":{"title":"Financial Reports","balanceSheet":"Balance Sheet","incomeStatement":"Income Statement","cashFlow":"Cash Flow","trialBalance":"Trial Balance","monthlyReport":"Monthly Report","yearlyReport":"Yearly Report"}}');

/***/ })

};
;