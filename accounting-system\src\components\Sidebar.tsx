'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  HomeIcon,
  DocumentTextIcon,
  BookOpenIcon,
  UsersIcon,
  TruckIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface SidebarProps {
  userRole: 'admin' | 'accountant' | 'employee';
}

export default function Sidebar({ userRole }: SidebarProps) {
  const t = useTranslations();
  const pathname = usePathname();
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    localStorage.removeItem('user');
    router.push('/login');
  };

  const navigation = [
    {
      name: t('navigation.dashboard'),
      href: '/dashboard',
      icon: HomeIcon,
      roles: ['admin', 'accountant', 'employee']
    },
    {
      name: t('navigation.invoices'),
      href: '/invoices',
      icon: DocumentTextIcon,
      roles: ['admin', 'accountant', 'employee']
    },
    {
      name: t('navigation.journalEntries'),
      href: '/journal-entries',
      icon: BookOpenIcon,
      roles: ['admin', 'accountant']
    },
    {
      name: t('navigation.customers'),
      href: '/customers',
      icon: UsersIcon,
      roles: ['admin', 'accountant', 'employee']
    },
    {
      name: t('navigation.suppliers'),
      href: '/suppliers',
      icon: TruckIcon,
      roles: ['admin', 'accountant', 'employee']
    },
    {
      name: t('navigation.reports'),
      href: '/reports',
      icon: ChartBarIcon,
      roles: ['admin', 'accountant']
    },
    {
      name: t('navigation.taxes'),
      href: '/taxes',
      icon: CurrencyDollarIcon,
      roles: ['admin', 'accountant']
    },
    {
      name: t('navigation.settings'),
      href: '/settings',
      icon: CogIcon,
      roles: ['admin']
    }
  ];

  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(userRole)
  );

  const isActive = (href: string) => {
    return pathname.includes(href);
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-white p-2 rounded-md shadow-md"
        >
          {isMobileMenuOpen ? (
            <XMarkIcon className="h-6 w-6" />
          ) : (
            <Bars3Icon className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-primary-600">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-white rounded-lg flex items-center justify-center">
                <svg className="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <span className="ml-2 text-white font-semibold text-sm">
                نظام المحاسبة
              </span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {filteredNavigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    sidebar-item
                    ${isActive(item.href) ? 'sidebar-item-active' : 'sidebar-item-inactive'}
                  `}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Icon className="h-5 w-5 ml-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info and logout */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 font-medium text-sm">
                  {userRole === 'admin' ? 'م' : userRole === 'accountant' ? 'ح' : 'ع'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">مستخدم تجريبي</p>
                <p className="text-xs text-gray-500">
                  {userRole === 'admin' ? 'مدير' : userRole === 'accountant' ? 'محاسب' : 'موظف'}
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md transition-colors duration-200"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 ml-3" />
              {t('navigation.logout')}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
