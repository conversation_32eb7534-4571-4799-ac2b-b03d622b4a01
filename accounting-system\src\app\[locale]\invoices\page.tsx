'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import DashboardLayout from '@/components/DashboardLayout';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

export default function InvoicesPage() {
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState<'outgoing' | 'incoming'>('outgoing');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API
  const outgoingInvoices = [
    {
      id: 1,
      invoiceNumber: 'INV-001',
      customer: 'شركة الأمل للتجارة',
      date: '2024-01-15',
      dueDate: '2024-02-15',
      amount: 15000,
      status: 'paid',
      items: 3
    },
    {
      id: 2,
      invoiceNumber: 'INV-002',
      customer: 'مؤسسة النور',
      date: '2024-01-14',
      dueDate: '2024-02-14',
      amount: 8500,
      status: 'pending',
      items: 2
    },
    {
      id: 3,
      invoiceNumber: 'INV-003',
      customer: 'شركة المستقبل',
      date: '2024-01-10',
      dueDate: '2024-02-10',
      amount: 12000,
      status: 'overdue',
      items: 4
    }
  ];

  const incomingInvoices = [
    {
      id: 1,
      invoiceNumber: 'BILL-001',
      supplier: 'شركة التوريدات المتقدمة',
      date: '2024-01-12',
      dueDate: '2024-02-12',
      amount: 6000,
      status: 'pending',
      items: 2
    },
    {
      id: 2,
      invoiceNumber: 'BILL-002',
      supplier: 'مؤسسة الخدمات',
      date: '2024-01-08',
      dueDate: '2024-02-08',
      amount: 3500,
      status: 'paid',
      items: 1
    }
  ];

  const currentInvoices = activeTab === 'outgoing' ? outgoingInvoices : incomingInvoices;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return t('invoices.paid');
      case 'pending':
        return t('invoices.pending');
      case 'overdue':
        return t('invoices.overdue');
      default:
        return status;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">{t('invoices.title')}</h1>
          <button className="btn-primary flex items-center">
            <PlusIcon className="h-5 w-5 ml-2" />
            {t('invoices.createInvoice')}
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('outgoing')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'outgoing'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {t('invoices.outgoing')}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {outgoingInvoices.length}
              </span>
            </button>
            <button
              onClick={() => setActiveTab('incoming')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'incoming'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {t('invoices.incoming')}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {incomingInvoices.length}
              </span>
            </button>
          </nav>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={`${t('common.search')} ${activeTab === 'outgoing' ? 'العملاء' : 'الموردين'}...`}
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex gap-2">
            <button className="btn-secondary flex items-center">
              <FunnelIcon className="h-5 w-5 ml-2" />
              {t('common.filter')}
            </button>
            <button className="btn-secondary flex items-center">
              <DocumentArrowDownIcon className="h-5 w-5 ml-2" />
              {t('common.export')}
            </button>
          </div>
        </div>

        {/* Invoices Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('invoices.invoiceNumber')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {activeTab === 'outgoing' ? t('invoices.customer') : 'المورد'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('invoices.date')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الاستحقاق
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('invoices.amount')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('invoices.status')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentInvoices.map((invoice) => (
                  <tr key={invoice.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {invoice.invoiceNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {activeTab === 'outgoing' ? (invoice as any).customer : (invoice as any).supplier}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invoice.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invoice.dueDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {invoice.amount.toLocaleString('ar-SA')} ر.س
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                        {getStatusText(invoice.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button className="text-primary-600 hover:text-primary-900">
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">إجمالي الفواتير</h3>
            <p className="text-3xl font-bold text-primary-600">{currentInvoices.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">إجمالي المبلغ</h3>
            <p className="text-3xl font-bold text-green-600">
              {currentInvoices.reduce((sum, inv) => sum + inv.amount, 0).toLocaleString('ar-SA')} ر.س
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">الفواتير المعلقة</h3>
            <p className="text-3xl font-bold text-yellow-600">
              {currentInvoices.filter(inv => inv.status === 'pending').length}
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
